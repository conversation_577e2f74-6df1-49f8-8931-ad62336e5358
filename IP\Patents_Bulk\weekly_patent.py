"""
This script handles the downloading, processing, and database upserting of USPTO patent data.
It processes weekly Patent Grant Full Text Data (PTGRDT)

Supported Modes:
- Grants:
    - latest: Process the most recent weekly grant file.
    - catchup: Process grant files from a start date to an end date.
    - specific_date: Process the grant file for a specific Tuesday date.
    
In the U.S., utility and plant patents are generally valid for 20 years from the date the patent application was filed. 
Design patents have a term of 15 years from the grant date for applications filed on or after May 13, 2015, and 14 years for those filed before that date.
=> Let's collect everything from June 2011 (14 years ago)
"""

import os, sys
sys.path.append(os.getcwd())
import asyncio
import logging
import argparse
import datetime
import time
from pathlib import Path
from dotenv import load_dotenv
from Common.Constants import local_ip_folder

from dateutil.relativedelta import relativedelta, MO, TU, WE, TH, FR, SA, SU # For finding specific weekdays

from IP.Patents_Bulk.patent_file import download_file, extract_patent_zip, cleanup_extracted_directory # send_to_nas, send_folder_to_nas removed as not used directly here anymore
from IP.Patents_Bulk.patent_parser import parse_grant_xml_parallel
from IP.Patents_Bulk.patent_db_grant import upsert_patent_grants
from IP.Patents_Bulk.patent_db_uspc import upsert_patent_uspc_assignments
from IP.Patents_Bulk.patent_db_cpc import upsert_patent_cpc_ipc_assignments
from IP.Patents_Bulk.weekly_patent_nas import perform_nas_backup_operations_internal
from IP.Patents_Bulk.embedding_service import EmbeddingQueue

# Load environment variables from .env file
load_dotenv()

# Configure logging
log_file_path = Path(__file__).parent / "logs/weekly_patent_report.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- Constants ---
# Base URLs (API endpoints)
# Grant files are typically released on Tuesdays. Filename format: IYYYYMMDD.zip
PTGRDT_API_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/{}/I{}{}"
# the cutoff date deciding whether assigning .zip or .tar for the API call
# file before 2009/12/29 end with "ZIP", file before 2010/10/1 end with "zip" and the file after end with "tar"
CUTOFF_DATE_ZIP = datetime.date(2009, 12, 29)
CUTOFF_DATE_zip = datetime.date(2010, 10, 1)

# Directory structure
BASE_DIR = Path(local_ip_folder) / "Patents"
GRANT_ZIP_DIR = BASE_DIR / "USPTO_Grants" / "Zip"
GRANT_EXTRACT_DIR_BASE = BASE_DIR / "USPTO_Grants" / "Extracted" # Base for extracted content, organized by zip name
CPC_ZIP_DIR = BASE_DIR / "USPTO_CPC" / "Zip"
CPC_EXTRACT_DIR_BASE = BASE_DIR / "USPTO_CPC" / "Extracted" # Base for extracted content, organized by zip name

# Create local directories
GRANT_ZIP_DIR.mkdir(parents=True, exist_ok=True)
GRANT_EXTRACT_DIR_BASE.mkdir(parents=True, exist_ok=True)
CPC_ZIP_DIR.mkdir(parents=True, exist_ok=True)
CPC_EXTRACT_DIR_BASE.mkdir(parents=True, exist_ok=True)


def get_last_tuesday(ref_date=None):
    """Finds the date of the most recent Tuesday relative to ref_date."""
    if ref_date is None:
        ref_date = datetime.date.today()
    # relativedelta(weekday=TU(-1)) finds the previous Tuesday
    return ref_date + relativedelta(weekday=TU(-1))

def get_tuesdays_in_range(start_date, end_date, direction="forward"):
    """
    Generates all Tuesdays within a given date range (inclusive).
    Direction can be "forward" (start_date to end_date) or "backward" (end_date to start_date).
    """
    if direction == "forward":
        current_date = start_date + relativedelta(weekday=TU(0)) # Find first Tuesday on or after start_date
        if current_date < start_date: # If start_date itself is Tuesday, TU(0) might go to next week
             current_date = start_date + relativedelta(weekday=TU(-1)) # Check previous Tuesday
             if current_date < start_date: # If previous is still before, start is after Tuesday, find next
                 current_date = start_date + relativedelta(weekday=TU(+1))

        while current_date <= end_date:
            yield current_date
            current_date += datetime.timedelta(weeks=1)
    elif direction == "backward":
        current_date = end_date + relativedelta(weekday=TU(0)) # Find first Tuesday on or after end_date
        # Adjust current_date to be the first Tuesday on or before end_date
        if current_date > end_date:
            current_date = end_date + relativedelta(weekday=TU(-1))
        
        while current_date >= start_date:
            yield current_date
            current_date -= datetime.timedelta(weeks=1)
    else:
        raise ValueError("Direction must be 'forward' or 'backward'.")


# --- Concurrency Configuration &amp; Pipeline Implementation ---
DEFAULT_DOWNLOAD_WORKERS = 1
DEFAULT_EXTRACT_WORKERS = 1
DEFAULT_PROCESS_WORKERS = 1
DEFAULT_DB_WORKERS = 1
DEFAULT_QUEUE_MAX_SIZE = 10 # Max items to buffer between stages

# Sentinel object to signal workers to stop
PIPELINE_SENTINEL = object()

async def downloader_worker(name: str,download_queue: asyncio.Queue,extract_queue: asyncio.Queue,nas_enabled: bool):
    """Fetches patent files and puts them on the extraction queue."""
    logger.info(f"[{name}] Starting...")
    while True:
        start_time = time.time() # Start timer for this task item
        item = await download_queue.get()
        if item is PIPELINE_SENTINEL:
            logger.info(f"[{name}] Received sentinel. Shutting down.")
            await extract_queue.put(PIPELINE_SENTINEL) # Propagate sentinel
            download_queue.task_done()
            break

        zip_url = item['zip_url']
        zip_filename = item['zip_filename']
        headers = item['headers']
        target_date = item['target_date']
        local_zip_path = GRANT_ZIP_DIR / zip_filename

        logger.info(f"[{name}] ({zip_filename}): Processing download from {zip_url}")

        try:
            # NAS Backup Operations (Fire-and-Forget)
            # if nas_enabled: # Check if NAS operations are globally enabled
                # Ensure perform_nas_backup_operations_internal is async or wrapped if not
                # It is already async from weekly_patent_nas.py
                # asyncio.create_task(
                #     perform_nas_backup_operations_internal(zip_filename=zip_filename, zip_url=zip_url, headers=headers),
                #     name=f"NAS_Backup_{zip_filename}"
                # )
                # logger.info(f"[{name}] ({zip_filename}): NAS backup task created.")


            if local_zip_path.exists():
                logger.info(f"[{name}] ({zip_filename}): File already exists locally at {local_zip_path}. Skipping download.")
                download_success = True
            else:
                download_success = await asyncio.to_thread(download_file, zip_url, str(local_zip_path), headers)

            if download_success:
                logger.info(f"[{name}] ({zip_filename}): Local download successful to {local_zip_path} in {int(time.time() - start_time)}s")
                await extract_queue.put({
                    'local_zip_path': local_zip_path,
                    'zip_filename': zip_filename,
                    'target_date': target_date,
                    # Pass original URL and headers if NAS backup is triggered by extractor
                    # or if downloader handles NAS and extractor doesn't need them.
                    # For now, downloader handles NAS, extractor just needs local_zip_path.
                })
            else:
                logger.warning(f"[{name}] ({zip_filename}): Local download failed. Skipping further processing for this file.")
                # No item put on extract_queue, so this file stops here.

        except Exception as e:
            logger.error(f"[{name}] ({zip_filename}): Error during download stage: {e}", exc_info=True)
        finally:
            download_queue.task_done()

async def extractor_worker(name: str,extract_queue: asyncio.Queue,process_queue: asyncio.Queue):
    """Extracts downloaded files and puts XML paths on the process queue."""
    logger.info(f"[{name}] Starting...")
    while True:
        start_time = time.time() # Start timer for this task item
        item = await extract_queue.get()
        if item is PIPELINE_SENTINEL:
            logger.info(f"[{name}] Received sentinel. Shutting down.")
            await process_queue.put(PIPELINE_SENTINEL) # Propagate sentinel
            extract_queue.task_done()
            break

        local_zip_path = item['local_zip_path']
        zip_filename = item['zip_filename']
        target_date = item['target_date']

        # Create a persistent extraction directory for this archive
        # e.g., USPTO_Grants/Extracted/********* (from *********.tar)
        zip_filename_stem = local_zip_path.stem # Removes .tar or .zip
        if zip_filename_stem.endswith('.tar'): # Handle cases like .tar.gz if any, though current is .tar
            zip_filename_stem = Path(zip_filename_stem).stem

        # persistent_extract_dir = GRANT_EXTRACT_DIR_BASE / zip_filename_stem
        # persistent_extract_dir.mkdir(parents=True, exist_ok=True)

        # logger.info(f"[{name}] ({zip_filename}): Extracting to {persistent_extract_dir}")

        try:
            all_extracted_files_paths, temp_extract_dir_for_cleanup = await asyncio.to_thread(
                extract_patent_zip, str(local_zip_path)
            )

            if not all_extracted_files_paths:
                logger.warning(f"[{name}] ({zip_filename}): No files extracted from {local_zip_path}.")
                # If no files extracted, ensure temp_extract_dir_for_cleanup is still cleaned up if it was created
                if temp_extract_dir_for_cleanup:
                    await asyncio.to_thread(cleanup_extracted_directory, str(temp_extract_dir_for_cleanup))
            else:
                xml_file_paths = [p for p in all_extracted_files_paths if Path(p).suffix.lower() == '.xml' and not Path(p).name.lower().endswith('s00001.xml')]
                if xml_file_paths:
                    logger.info(f"[{name}] ({zip_filename}): Extracted {len(xml_file_paths)} XML files in {int(time.time() - start_time)}s")
                    await process_queue.put({
                        'xml_file_paths': xml_file_paths,
                        'extraction_dir': GRANT_EXTRACT_DIR_BASE,
                        'zip_filename': zip_filename,
                        'target_date': target_date,
                        'temp_cleanup_dir': temp_extract_dir_for_cleanup
                    })
                else:
                    # If XML files are not found after extraction, clean up the temporary directory
                    if temp_extract_dir_for_cleanup:
                        await asyncio.to_thread(cleanup_extracted_directory, str(temp_extract_dir_for_cleanup))
        except Exception as e:
            logger.error(f"[{name}] ({zip_filename}): Error during extraction stage: {e}", exc_info=True)
            # If extraction fails, this file stops here.
            # Cleanup of potentially partially extracted 'persistent_extract_dir' could be added here,
            # or left for a manual/separate cleanup process if errors are rare.
            # For now, leave as is; DB worker cleans up on success.
        finally:
            extract_queue.task_done()

async def processor_worker(name: str, process_queue: asyncio.Queue, db_queue: asyncio.Queue, mode: str, embedding_queue: EmbeddingQueue):
    """Parses XML files and puts parsed data on the DB queue."""
    logger.info(f"[{name}] Starting...")
    while True:
        start_time = time.time() # Start timer for this task item
        item = await process_queue.get()
        if item is PIPELINE_SENTINEL:
            logger.info(f"[{name}] Received sentinel. Shutting down.")
            await db_queue.put(PIPELINE_SENTINEL) # Propagate sentinel
            process_queue.task_done()
            break

        xml_file_paths = item['xml_file_paths']
        extraction_dir = item['extraction_dir']
        zip_filename = item['zip_filename']
        target_date = item['target_date']

        logger.info(f"[{name}] ({zip_filename}): Starting XML parsing for {len(xml_file_paths)} files.")
        try:
            # parse_grant_xml_parallel uses a multiprocessing pool internally
            # Pass the extraction_dir to the parallel parser
            parsed_data_list, items_to_enqueue_for_embedding = await asyncio.to_thread(parse_grant_xml_parallel, xml_file_paths, mode, extraction_dir)

            if items_to_enqueue_for_embedding:
                images_enqueued, images_skipped_existing = await embedding_queue.batch_enqueue(items_to_enqueue_for_embedding)
                logger.info(f"[{name}] ({zip_filename}): Enqueued {images_enqueued} images for embedding processing. Skipped {images_skipped_existing} images as they already existed in the database.")

            if not parsed_data_list:
                logger.warning(f"[{name}] ({zip_filename}): No records obtained after parsing XMLs.")
            else:
                logger.info(f"[{name}] ({zip_filename}): Parsed {len(parsed_data_list)} records in {int(time.time() - start_time)}s")
                await db_queue.put({
                    'parsed_data': parsed_data_list,
                    'extraction_dir': extraction_dir, # Keep this as is
                    'zip_filename': zip_filename,
                    'target_date': target_date,
                    'num_records': len(parsed_data_list),
                    'temp_cleanup_dir': item.get('temp_cleanup_dir') # Pass the new temporary directory path
                })
        except Exception as e:
            logger.error(f"[{name}] ({zip_filename}): Error during XML processing stage: {e}", exc_info=True)
            # This file stops here if parsing fails.
        finally:
            process_queue.task_done()

async def db_worker(name: str, db_queue: asyncio.Queue, results_queue: asyncio.Queue, mode: str):
    """Upserts parsed data into the database and cleans up extraction directory."""
    logger.info(f"[{name}] Starting...")
    while True:
        start_time = time.time() # Start timer for this task item
        item = await db_queue.get()
        if item is PIPELINE_SENTINEL:
            logger.info(f"[{name}] Received sentinel. Shutting down.")
            # This is the last stage for data, so it signals the results_queue
            await results_queue.put(PIPELINE_SENTINEL)
            db_queue.task_done()
            break

        parsed_data_list = item['parsed_data']
        extraction_dir = item['extraction_dir']
        zip_filename = item['zip_filename']
        num_records = item['num_records']
        # target_date = item['target_date'] # Available if needed for logging

        logger.info(f"[{name}] ({zip_filename}): Starting database operations for {num_records} records.")
        db_success = False
        try:
            # Upsert main grant data (includes Locarno, USPC directly)
            grants_committed_count = await asyncio.to_thread(upsert_patent_grants, parsed_data_list, mode)
            logger.info(f"[{name}] ({zip_filename}): upsert_patent_grants committed {grants_committed_count} records (i.e. went into database).")

            # Upsert CPC assignments (links patents_records to patents_cpc_definitions)
            cpc_assignments_committed_count = await asyncio.to_thread(upsert_patent_cpc_ipc_assignments, parsed_data_list, mode)
            logger.info(f"[{name}] ({zip_filename}): upsert_patent_cpc_ipc_assignments processed {cpc_assignments_committed_count} assignment entries (i.e. went into database).")

            # Upsert USPC assignments (links patents_records to patents_uspc_definitions)
            uspc_assignments_committed_count = await asyncio.to_thread(upsert_patent_uspc_assignments, parsed_data_list, mode)
            logger.info(f"[{name}] ({zip_filename}): upsert_patent_uspc_assignments processed {uspc_assignments_committed_count} assignment entries (i.e. went into database).")

            # Consider all successful if no exceptions were raised and counts are reasonable.
            # For simplicity, assume success if no exceptions.
            db_success = True
            await results_queue.put({'zip_filename': zip_filename, 'records_processed': num_records, 'status': 'success'})

        except Exception as e:
            logger.error(f"[{name}] ({zip_filename}): Error during database operations: {e}", exc_info=True)
            await results_queue.put({'zip_filename': zip_filename, 'records_processed': 0, 'status': 'db_error'})
        finally:
            temp_cleanup_dir = item.get('temp_cleanup_dir')
            if temp_cleanup_dir: # Only attempt cleanup if the path exists
                try:
                    # Always attempt to clean up the temporary extraction directory
                    logger.info(f"[{name}] ({zip_filename}): Attempting to clean up temporary extraction directory: {temp_cleanup_dir}.")
                    await asyncio.to_thread(cleanup_extracted_directory, str(temp_cleanup_dir))
                except Exception as e_cleanup:
                    logger.error(f"[{name}] ({zip_filename}): Error cleaning up temporary extraction directory {temp_cleanup_dir}: {e_cleanup}", exc_info=True)
            else:
                logger.warning(f"[{name}] ({zip_filename}): No temporary cleanup directory provided for cleanup.")
            db_queue.task_done()

async def process_grants_for_period_concurrent(
    mode, start_date, end_date, headers,
    num_download_workers=DEFAULT_DOWNLOAD_WORKERS,
    num_extract_workers=DEFAULT_EXTRACT_WORKERS,
    num_process_workers=DEFAULT_PROCESS_WORKERS,
    num_db_workers=DEFAULT_DB_WORKERS,
    queue_max_size=DEFAULT_QUEUE_MAX_SIZE,
    nas_enabled=False, # Master switch for NAS operations - Disabled NAS transfer as requested
    direction="forward" # New parameter: "forward" (start to end) or "backward" (end to start)
):
    """Processes weekly grant files concurrently using a pipeline."""
    logger.info(f"Starting CONCURRENT Grant processing from {start_date} to {end_date} in {direction} direction.")
    logger.info(f"Worker config: DL={num_download_workers}, EXT={num_extract_workers}, PROC={num_process_workers}, DB={num_db_workers}")
    logger.info(f"Queue max size: {queue_max_size}, NAS operations enabled: {nas_enabled}")

    download_q = asyncio.Queue(maxsize=queue_max_size)
    extract_q = asyncio.Queue(maxsize=queue_max_size)
    process_q = asyncio.Queue(maxsize=queue_max_size)
    db_q = asyncio.Queue(maxsize=queue_max_size)
    results_q = asyncio.Queue() # For collecting results from DB workers
    
    embedding_queue = EmbeddingQueue(max_concurrent=3, num_workers=10)
    
    try: 
        await embedding_queue.start()
        logger.info(f"Embedding queue started with status: {embedding_queue.get_queue_status()}")

        # --- Create and start worker tasks ---
        workers = []
        for i in range(num_download_workers):
            task = asyncio.create_task(downloader_worker(f"Downloader-{i+1}", download_q, extract_q, nas_enabled))
            workers.append(task)
        for i in range(num_extract_workers):
            task = asyncio.create_task(extractor_worker(f"Extractor-{i+1}", extract_q, process_q))
            workers.append(task)
        for i in range(num_process_workers):
            task = asyncio.create_task(processor_worker(f"Processor-{i+1}", process_q, db_q, mode, embedding_queue))
            workers.append(task)
        for i in range(num_db_workers):
            task = asyncio.create_task(db_worker(f"DBWorker-{i+1}", db_q, results_q, mode))
            workers.append(task)

        # --- Populate initial download queue ---
        files_to_process_details = []
        for target_date in get_tuesdays_in_range(start_date, end_date, direction):
            date_str = target_date.strftime("%Y%m%d")

            if target_date <= CUTOFF_DATE_ZIP:
                file_type = ".ZIP"
            elif target_date <= CUTOFF_DATE_zip:
                file_type = ".zip"
            else:
                file_type = ".tar"

            zip_filename = f"I{date_str}{file_type}"
            zip_url = PTGRDT_API_BASE_URL.format(target_date.year, date_str, file_type)
            files_to_process_details.append({
                'zip_url': zip_url,
                'zip_filename': zip_filename,
                'headers': headers,
                'target_date': target_date
            })

        if not files_to_process_details:
            logger.info("No grant files found for the given period.")
        else:
            logger.info(f"Found {len(files_to_process_details)} grant files to process.")
            for detail in files_to_process_details:
                await download_q.put(detail)

        # --- Signal end of input & wait for queues to empty ---
        logger.info("All initial files queued for download. Adding sentinels to download queue.")
        for _ in range(num_download_workers):
            await download_q.put(PIPELINE_SENTINEL)

        # Wait for all items in queues to be processed
        # This ensures that sentinels propagate and workers finish their current tasks.
        await download_q.join()
        logger.info("Download queue joined.")
        await extract_q.join()
        logger.info("Extract queue joined.")
        await process_q.join()
        logger.info("Process queue joined.")
        await db_q.join()
        logger.info("DB queue joined.")

        # --- Collect results ---
        total_records_committed = 0
        successful_files = 0
        failed_files = 0
        # The results_q will receive one sentinel per DB worker.
        # We also need to count the actual file results.

        db_workers_finished_signaling = 0
        while db_workers_finished_signaling < num_db_workers:
            result_item = await results_q.get()
            if result_item is PIPELINE_SENTINEL:
                db_workers_finished_signaling += 1
            else:
                # This is a result from a file processed by a DB worker
                logger.info(f"Result from DB: File {result_item['zip_filename']} - Status: {result_item['status']}, Records: {result_item['records_processed']}")
                if result_item['status'] == 'success':
                    total_records_committed += result_item['records_processed']
                    successful_files +=1
                else:
                    failed_files +=1
            results_q.task_done()

        logger.info("All results collected from DB workers.")

        # --- Wait for all worker tasks to complete (they exit after processing sentinels) ---
        await asyncio.gather(*workers, return_exceptions=True) # Capture exceptions from workers if any
        logger.info("All worker tasks have completed.")

        logger.info(f"\n=== Concurrent Grant Processing Summary ({start_date} to {end_date}) ===")
        logger.info(f"Attempted to process {len(files_to_process_details)} grant files.")
        logger.info(f"Successfully processed files (reached DB commit): {successful_files}")
        logger.info(f"Failed files (did not complete all stages): {failed_files}") # This count might need refinement based on where failure is defined
        logger.info(f"Total records committed to database: {total_records_committed}")
        logger.info("===================================================================")
        
        logger.info(f"Waiting for embedding queue to drain. Status: {embedding_queue.get_queue_status()}")
        await embedding_queue.drain()
        logger.info("All embedding tasks completed")
        
    finally:
        # Always stop the workers gracefully
        await embedding_queue.stop()

    return successful_files, total_records_committed # Or a more detailed summary object


async def run_weekly_patent_grants_pipeline():
    """
    Runs the weekly patent grant processing pipeline.
    Fetches the latest weekly patent data from USPTO,
    processes it, and loads it into the database.
    """
    logger.info("Starting weekly patent grants pipeline.")

    try:
        # Setup USPTO API headers
        api_key = os.getenv("USPTO_ODP_API_KEY")
        headers = {"X-API-KEY": api_key} if api_key else None
        if not api_key:
            logger.warning("USPTO_ODP_API_KEY not found in environment variables. Downloads may fail.")

        # Get the date of the last Tuesday for processing
        target_date = get_last_tuesday()
        logger.info(f"Processing grants for the week ending: {target_date}")

        # Run the asynchronous processing pipeline
        # Default direction is "forward" (start_date to end_date)
        processed_files, total_records = await process_grants_for_period_concurrent(target_date, target_date, headers, direction="forward")

        logger.info(f"Weekly patent grants pipeline completed successfully for {target_date}.")
        return processed_files, total_records

    except Exception as e:
        logger.error(f"Error during weekly patent grants pipeline: {e}", exc_info=True)
        raise # Re-raise the exception after logging


# --- Main Function & Argparse ---
async def main():
    """Main function to parse arguments and orchestrate processing."""
    print("DEBUG: Entered main function") # Added for debugging
    parser = argparse.ArgumentParser(description="USPTO Patent Data Processing Orchestrator")
    parser.add_argument("--type", choices=["grant", "cpc"], required=True,
                        help="Type of data to process: weekly grants or monthly CPC.")
    parser.add_argument("--mode", choices=["latest", "catchup", "specific_date"], required=True,
                        help="Processing mode: 'latest', 'catchup' (requires date range), 'specific_date' (requires --date).")
    parser.add_argument("--start-date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="Start date for 'catchup' mode (YYYY-MM-DD).")
    parser.add_argument("--end-date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="End date for 'catchup' mode (YYYY-MM-DD). Defaults to today if not provided.")
    parser.add_argument("--date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="Specific date for 'specific_date' mode (YYYY-MM-DD). Should be a Tuesday for grants.")
    parser.add_argument("--direction", choices=["forward", "backward"], default="forward",
                        help="Direction for processing dates in 'catchup' mode: 'forward' (start to end) or 'backward' (end to start). Default is 'forward'.")
    # parser.add_argument("--file", type=str, help="Specific file URL or path for 'specific_file' mode.") # Optional: Add if specific file mode is needed

    args = parser.parse_args()

    # Setup USPTO API headers
    api_key = os.getenv("USPTO_ODP_API_KEY") # Using the same key as trademarks for now
    if not api_key:
        logger.warning("USPTO_ODP_API_KEY not found in .env file. Downloads may fail.")
        headers = None
    else:
        headers = {"X-API-KEY": api_key}

    start_time = time.time()

    if args.mode == "latest":
        target_date = get_last_tuesday()
        logger.info(f"Processing latest grant file (estimated: {target_date})")
        await process_grants_for_period_concurrent(target_date, target_date, headers)
    elif args.mode == "catchup":
        if not args.start_date:
            parser.error("--start-date is required for catchup mode.")
        end_date = args.end_date if args.end_date else datetime.date.today()
        if args.start_date > end_date and args.direction == "forward":
                parser.error("--start-date cannot be after --end-date in 'forward' direction.")
        if args.start_date < end_date and args.direction == "backward":
                parser.error("--end-date cannot be after --start-date in 'backward' direction.")
        await process_grants_for_period_concurrent(args.start_date, end_date, headers, direction=args.direction)
    elif args.mode == "specific_date":
        if not args.date:
            parser.error("--date is required for specific_date mode.")
        # Optional: Add check if date is a Tuesday
        if args.date.weekday() != 1: # Monday is 0, Tuesday is 1
                logger.warning(f"Specified date {args.date} is not a Tuesday. Grant files are typically released on Tuesdays.")
        await process_grants_for_period_concurrent(args.date, args.date, headers, direction="forward") # Specific date always processes forward

    end_time = time.time()
    total_duration = end_time - start_time
    logger.info(f"\n=== Total Script Execution Time: {total_duration:.2f} seconds ===")


async def test_processing(mode="subset"):
    """
    Test function to process a specific date (2025-03-15).
    """
    
    startdate = datetime.date(2011, 6, 15)
    enddate = datetime.date(2025, 6, 22)
    # enddate = datetime.date(2011, 6, 16)
    logger.info(f"Running test processing for {startdate} to {enddate}...")

    # Setup API headers
    # api_key = os.getenv("USPTO_BULK_API_KEY")
    api_key = os.getenv("USPTO_ODP_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Process the test date directly with default direction (forward)
    processed_files, total_records = await process_grants_for_period_concurrent(mode, startdate, enddate, headers, direction="forward")

    logger.info(f"Test processing completed. Files processed: {processed_files}, Total records: {total_records}")
    return processed_files, total_records


if __name__ == "__main__":
    asyncio.run(test_processing(mode="subset")) # Options are "all" and "subset" depending on weather we want only certain utility patents

    # if in subset model, we save
    # -- all image of design patents
    # -- part of the images of utility patents
    # -- all the meta information of all the patents into data base (patents, patents_cpc_ipc_assignments, patents_uspc_assignments)

    # if in all model, we only save
    # -- all the meta information of all the patents into data base (patents, patents_cpc_ipc_assignments_all, patents_uspc_assignments_all), 



    # asyncio.run(main())