
import hashlib
import os

def generate_obfuscated_key(reg_no: str) -> str:
    """
    Generates an obfuscated key for an image filename using SHA256.
    
    Args:
        reg_no (str): The registration number of the IP asset.
        secret (bytes): A secret byte string used to salt the hash.
                        Defaults to _IMAGE_SECRET.
                        
    Returns:
        str: The SHA256 hexdigest of the combined reg_no and secret.
    """
    key_raw = reg_no.encode('utf-8') + os.getenv("IMAGE_SECRET").encode("utf-8")
    return hashlib.sha256(key_raw).hexdigest()

# def create_ip_url(plaintiff_id, image_filename):
#     """Create IP URL with proper formatting"""
#     IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_filename}"
#     IP_Url_new = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_filename}"
#     return IP_Url

def create_ip_url(ip_type, seed): # For Trademark the seed is ser_no, for Copyright the seed is reg_no, for Patent the seed is basename(filename) which is the reg_no with the page number
    """Create IP URL with proper formatting"""
    obfuscated_reg_no = generate_obfuscated_key(seed)
    if ip_type == "Patent":
        extension = "png"
    else:
        extension = "webp"
    
    IP_Url = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/{ip_type}s/{obfuscated_reg_no}.{extension}"
    
    return IP_Url