# IP/Patents/patent_image.py
import os
import logging
import json

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def process_patent_images(patent_records, image_extraction_dir):
    """
    Verifies the existence of extracted patent image files and updates patent records.

    Args:
        patent_records (list): A list of dictionaries, where each dictionary represents
                               a parsed patent record. Grant records should contain
                               an 'image_filenames' key with a list of expected image filenames.
        image_extraction_dir (str): The path to the directory where the patent XML
                                    and its associated TIFF images were extracted.
    """
    if not isinstance(patent_records, list):
        logging.error("Invalid input: patent_records must be a list.")
        return # Or raise an error

    if not os.path.isdir(image_extraction_dir):
        logging.error(f"Image extraction directory not found or not a directory: {image_extraction_dir}")
        # Depending on requirements, might want to raise an error or return early

    for record in patent_records:
        if not isinstance(record, dict):
            logging.warning("Skipping invalid record item (not a dictionary).")
            continue

        # Determine if it's a grant record with image filenames
        # Adjust this condition based on the actual structure provided by patent_parser.py
        is_grant_with_images = 'image_filenames' in record and isinstance(record.get('image_filenames'), list) and record['image_filenames']

        if is_grant_with_images:
            found_images = []
            patent_id = record.get('publication_number', 'UnknownID') # Use a patent identifier for logging

            for image_filename in record['image_filenames']:
                if not isinstance(image_filename, str) or not image_filename:
                    logging.warning(f"Patent {patent_id}: Invalid image filename found in list: {image_filename}")
                    continue

                expected_image_path = os.path.join(image_extraction_dir, image_filename)

                if os.path.exists(expected_image_path) and os.path.isfile(expected_image_path):
                    # Store the relative filename as found
                    found_images.append(image_filename)
                    logging.info(f"Patent {patent_id}: Image found: {image_filename}")
                else:
                    logging.warning(f"Patent {patent_id}: Image NOT found: {image_filename} (Expected at: {expected_image_path})")

            # Update the record's image_source field
            if found_images:
                # Store as a JSON string list
                record['image_source'] = json.dumps(found_images)
                logging.info(f"Patent {patent_id}: Updated image_source with {len(found_images)} found images.")
            else:
                # No expected images were found
                record['image_source'] = None
                logging.info(f"Patent {patent_id}: No expected images found. Set image_source to None.")

        else:
            # Not a grant record with images, or image_filenames list is empty/missing
            record['image_source'] = None
            # Optionally log this case if needed for debugging
            # patent_id = record.get('publication_number', 'UnknownID')
            # logging.debug(f"Patent {patent_id}: Not a grant with images or no images listed. Set image_source to None.")

    # The list patent_records is modified in place.

# Example Usage Block (can be removed or kept for module testing)
if __name__ == '__main__':
    # Create a dummy extraction directory and files for testing
    dummy_dir = "temp_patent_images_test_run"
    if not os.path.exists(dummy_dir):
        os.makedirs(dummy_dir)
        print(f"Created dummy directory: {dummy_dir}")

    # Create dummy image files
    dummy_image_1 = "US12345-20230101-I00001.TIF"
    dummy_image_2 = "US12345-20230101-I00002.TIF"
    dummy_image_missing = "US12345-20230101-I00003.TIF"
    path1 = os.path.join(dummy_dir, dummy_image_1)
    path2 = os.path.join(dummy_dir, dummy_image_2)
    with open(path1, 'w') as f: f.write("dummy tiff data 1")
    with open(path2, 'w') as f: f.write("dummy tiff data 2")
    print(f"Created dummy files: {path1}, {path2}")

    # Sample patent records
    sample_records = [
        {
            'publication_number': 'US12345',
            'title': 'Test Patent 1',
            'image_filenames': [dummy_image_1, dummy_image_2, dummy_image_missing]
        },
        {
            'publication_number': 'US67890',
            'title': 'Test Patent 2 (No Images Expected)',
            'image_filenames': []
        },
        {
            'publication_number': 'US11223',
            'title': 'Test Patent 3 (Image Missing)',
            'image_filenames': [dummy_image_missing]
        },
        {
            'record_type': 'application', # Not a grant
            'application_number': 'APP999'
        }
    ]

    print("\n--- Before Processing ---")
    for r in sample_records: print(r)

    process_patent_images(sample_records, dummy_dir)

    print("\n--- After Processing ---")
    for r in sample_records: print(r)

    # Clean up dummy files and directory
    try:
        os.remove(path1)
        os.remove(path2)
        os.rmdir(dummy_dir)
        print(f"\nCleaned up dummy directory and files: {dummy_dir}")
    except OSError as e:
        print(f"Error cleaning up dummy directory: {e}")