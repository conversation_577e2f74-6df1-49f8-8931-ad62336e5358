"""
Reverse check endpoint for comparing IP assets against product images.
"""
import os, sys
sys.path.append(os.getcwd())
from fastapi import APIRouter, Depends
from qdrant_client.http import models
import tempfile
import json
from datetime import date
from langfuse import observe
import asyncio

from Check.Do_Check_Download import download_from_url
from Check.Create_Report import create_check_report, create_product_url
from Check.Utils import create_ip_url
from Common.Constants import local_ip_folder

if os.name != "nt":
    from utils.auth import verify_token
    from utils.db import get_db_connection
    from models.schemas import ReverseCheckRequest, ReverseCheckResponse, IPAssetInfringement, ProductPointInfringement, IPAsset
    from services.qdrant_service import upsert_ip_assets, query_batch_points
else:
    # Use this if running local
    from Qdrant.api.utils.auth import verify_token
    from Qdrant.api.utils.db import get_db_connection
    from Qdrant.api.models.schemas import ReverseCheckRequest, ReverseCheckResponse, IPAsset
    from Qdrant.api.services.qdrant_service import upsert_ip_assets, query_batch_points

router = APIRouter()

@router.post("/reverse_check", response_model=ReverseCheckResponse, dependencies=[Depends(verify_token)])
@observe()
async def reverse_check(request: ReverseCheckRequest):
    """
    Reverse Check endpoint.
    Compare newly added IP assets against the database of user-submitted product images.
    
    Args:
        request: The reverse check request.
        
    Returns:
        The reverse check response.
    """
    # 1. Insert IP Assets into the database: not needed! 
    # upsert_ip_asset_to_database(request)
    
    # 2. Insert IP Assets into Qdrant: ????
    
    # 3. Query Qdrant for similar Product Images
    # Prepare batch queries for Product_Images collection
    
    # Configuration for different IP types' query parameters
    IP_TYPE_QUERY_CONFIG = {"Copyright": {"threshold": 0.7, "limit": 300},
                            "Patent": {"threshold": 0.8, "limit": 300},
                            "Trademark": {"threshold": 0.65, "limit": 300}}

    ### Retrieve similar product images: 1 by 1 (both work, I have not compared speed)
    # query_requests = []
    # querried_ip_assets = []
    # for ip_asset in request.ip_assets:
    #     ip_type = ip_asset.ip_type
    #     if ip_asset.siglip_vector and len(ip_asset.siglip_vector) == 1024:
    #         query_requests.append({
    #             "query_vector": ("siglip_vector", ip_asset.siglip_vector),
    #             "limit": IP_TYPE_QUERY_CONFIG[ip_type]["limit"],
    #             "score_threshold": IP_TYPE_QUERY_CONFIG[ip_type]["threshold"],
    #             "with_payload": models.PayloadSelectorInclude(include=["client_id", "check_id", "filename"]),
    #             "with_vectors": False
    #         })
    #         querried_ip_assets.append(ip_asset)

    # # Execute batch query
    # qdrant_batch_results = query_batch_points("Product_Images", query_requests)
    
    print(f"reverse check request received: {request}")
    
    
    ### Retrieve similar product images: Batch (both work, I have not compared speed)
    query_requests = []
    querried_ip_assets = []
    for ip_asset in request.ip_assets:
        ip_type = ip_asset.ip_type
        query_requests.append(models.QueryRequest(
                query=ip_asset.siglip_vector,
                using="siglip_vector",
                limit=IP_TYPE_QUERY_CONFIG[ip_type]["limit"],
                score_threshold=IP_TYPE_QUERY_CONFIG[ip_type]["threshold"],
                with_payload=True,
                with_vector=False
            ))
        querried_ip_assets.append(ip_asset)
        
    print(f"query_requests: {query_requests}")
        
    qdrant_batch_results = query_batch_points("Product_Images", query_requests)
    
    print(f"qdrant_batch_results: {qdrant_batch_results}")
    
    
    # 4. Run the reports and filter the results
    infringements = []
    
    # For each IP_Asset
    with tempfile.TemporaryDirectory() as temp_dir:
        for query_index, ip_asset in enumerate(querried_ip_assets):
            # Download the IP Asset image? Not it is already local.
            ip_asset_paths, ip_urls = get_ip_asset_path(ip_asset)
            # ip_asset_paths = [r"D:\Win10User\Downloads\ReverseCheckTest\IP1.webp"]
             
            # For each match of a single IP_Asset
            non_match = 0
            for scored_point in qdrant_batch_results[query_index].points:
                product_image_path = await download_product_image(temp_dir, scored_point.payload["check_id"], scored_point.payload["filename"])
                # product_image_path = r"D:\Win10User\Downloads\ReverseCheckTest\Product1.webp"
                
                result = await create_check_report(
                    ip_type=ip_asset.ip_type,
                    check_id=scored_point.payload["check_id"],
                    result={
                        'ip_owner': ip_asset.metadata["ip_owner"],
                        'reg_no': ip_asset.metadata["reg_no"],
                        'product_local_path': product_image_path,
                        'ip_local_paths': ip_asset_paths,
                        'ip_asset_urls': ip_urls
                    }
                )
                
                # If result is none (if the IP picture is not there or Report Not required) or "risk_level" is missing (should never be the case) or Score <= 2
                if not result or not result.get("risk_level", None) or result["risk_level"] == "低风险": 
                    non_match += 1
                else:
                    non_match = 0
                    
                    # Put the result in the database
                    infringements.append({
                        "date_added": date.today(),
                        "client_id": scored_point.payload["client_id"],
                        "check_id": scored_point.payload["check_id"],
                        "results": [result]
                        })
                    print(f"infringement appened")
                    
                if non_match >= 5:
                    break
                
            # Consolidate by check_id: i.e. if one IP match against product picture_1 and product picture_3 of the same check_id, we want to consolidate the results
            check_ids_seen = []
            infringements_to_remove = []
            for inf in infringements:
                check_id = inf["check_id"]
                if check_id not in check_ids_seen:
                    check_ids_seen.append(check_id)
                else:
                    existing_inf = next((inf for inf in infringements if inf["check_id"] == check_id), None)
                    inf["results"].extend(existing_inf["results"])
                    infringements_to_remove.append(existing_inf)
            infringements = [inf for inf in infringements if inf not in infringements_to_remove]
 
            for inf in infringements:
                for result in inf["results"]:
                    result.pop("product_local_path")
                    result.pop("ip_local_paths")
            
            # Upload to database
            conn = get_db_connection()
            cursor = conn.cursor()
            for inf in infringements:
                cursor.execute(
                    "INSERT INTO reverse_check_result (date_added, client_id, check_id, results) VALUES (%s, %s, %s, %s)",
                    (inf["date_added"], inf["client_id"], int(inf["check_id"]), json.dumps(inf["results"]))
                )
            conn.commit()

    cursor.close()
    conn.close()

    # Return the response in the expected format
    return ReverseCheckResponse(results=[])


async def download_product_image(temp_dir, check_id, filename):
    product_image_path = os.path.join(temp_dir, filename)
    product_image_url = create_product_url(check_id, product_image_path)
    await download_from_url(product_image_url, product_image_path)
    return product_image_path

def get_ip_asset_path(ip_asset):
    if ip_asset.ip_type == "Copyright":
        ip_asset_path =  [os.path.join(local_ip_folder, "Copyrights", ip_asset.metadata["reg_no"] + ".webp")]
        ip_url = [create_ip_url(ip_asset.ip_type, ip_asset.metadata["reg_no"])]
    elif ip_asset.ip_type == "Trademark":
        ip_asset_path = [os.path.join(local_ip_folder, "Trademarks", "USPTO_Daily" , "Images", ip_asset.metadata["ser_no"][-2:], ip_asset.metadata["ser_no"][-4:-2], ip_asset.metadata["ser_no"] + ".webp")]
        ip_url = [create_ip_url(ip_asset.ip_type, ip_asset.metadata["ser_no"])]
    elif ip_asset.ip_type == "Patent":
        patent_patent_folder = os.path.join(local_ip_folder, "Patents", "USPTO_Grants", "Extracted", ip_asset.metadata["reg_no"][-2:], ip_asset.metadata["reg_no"][-4:-2])
        # Find the folder which include the reg_no
        patent_folder = next((f for f in os.listdir(patent_patent_folder) if ip_asset.metadata["reg_no"] in f), None)
        ip_asset_path = []
        ip_url = []
        for png_file in os.listdir(os.path.join(patent_patent_folder, patent_folder)):
            if png_file.endswith(".png"):
                ip_asset_path.append(os.path.join(patent_patent_folder, patent_folder, png_file))
                ip_url.append(create_ip_url(ip_asset.ip_type, os.path.splitext(os.path.basename(png_file))[0]))
    
    return ip_asset_path, ip_url

    
async def upsert_ip_asset_to_database(request):
    # Prepare IP assets for upsertion into PostgreSQL
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Consolidated database insertion
    table_mapping = {"Copyright": "copyrights","Patent": "patents","Trademark": "trademarks"}
    for ip_asset in request.ip_assets:
        table_name = table_mapping.get(ip_asset.ip_type)
        if table_name:
            columns = ", ".join(ip_asset.metadata.keys())
            placeholders = ", ".join(["%s"] * len(ip_asset.metadata))
            values = list(ip_asset.metadata.values())
            
            cursor.execute(
                f"INSERT INTO {table_name} (id, {columns}) VALUES (%s, {placeholders}) "
                f"ON CONFLICT (id) DO UPDATE SET {', '.join([f'{k} = EXCLUDED.{k}' for k in ip_asset.metadata.keys()])}",
                [ip_asset.id] + values
            )
            conn.commit()
    
    # Upsert IP assets into IP_Assets collection
    upsert_ip_assets(request.ip_assets)
    
if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.getcwd(), "..", "..", ".env"))

    ### Local reverse check: set ip_asset_paths and product_local_path in function above to fixed values
    # from Check.RAG.siglip_model import SiglipModel # Import SiglipModel
    # from utils.config import POSTGRES_HOST
    # POSTGRES_HOST = "vectorstore1.maidalv.com"

    # # Initialize SiglipModel
    # siglip_config = {
    #     "vector_size": 1024,
    #     "model_name_or_path": "google/siglip2-large-patch16-512"
    # }
    # siglip_model = SiglipModel(model_id="siglip2_large_patch16_512", config=siglip_config)
    # siglip_model.load() # Load the model

    # image_path = r"D:\Win10User\Downloads\ReverseCheckTest\IP1.webp"
    # ser_no = "test_ip_asset_ser_no" # A dummy serial number for the embedding service

    # # Get the actual SigLIP vector using the SiglipModel
    # actual_siglip_vector = siglip_model.compute_features(data_list=[image_path], data_type="image")[0].tolist()

    # # Create a new IP asset with the actual SigLIP vector
    # ip_asset = IPAsset(
    #     ip_type="Copyright", # Assuming Trademark for this test image
    #     id="111111111111", # Use the generated point_id as the IP asset ID
    #     metadata={"ser_no": ser_no, "ip_owner": "Test Owner", "reg_no": "TestRegNo"}, # Add necessary metadata
    #     siglip_vector=actual_siglip_vector
    # )

    # # Create a ReverseCheckRequest object
    # request = ReverseCheckRequest(
    #     ip_assets=[ip_asset],
    #     threshold=None,
    #     top_n=None
    # )

    # # Call the reverse_check function
    # response = asyncio.run(reverse_check(request))
    # print(f"Reverse check response: {response}", level='INFO')
    
    
    
    ### Server reverse check
    async def server_reverse_check_example():
        from Check.RAG.siglip_model import SiglipModel # Import SiglipModel
        import aiohttp
        import Common.uuid_utils

        # Initialize SiglipModel
        siglip_config = {
            "vector_size": 1024,
            "model_name_or_path": "google/siglip2-large-patch16-512"
        }
        siglip_model = SiglipModel(model_id="siglip2_large_patch16_512", config=siglip_config)
        siglip_model.load() # Load the model
        
        # image_path = r"D:\Win10User\Downloads\ReverseCheckTest2\VA0002374455.webp"
        # reg_no = "VA0002374455"
        # ip_type = "Copyright"
        # image_path = r"D:\Win10User\Downloads\ReverseCheckTest2\85587416.webp"
        # reg_no = "85587416"  # It is the ser_no
        # ip_type = "Trademark"
        image_path = r"D:\Win10User\Downloads\ReverseCheckTest2\D1027376.webp"
        reg_no = "D1027376"
        ip_type = "Patent"

        # Get the actual SigLIP vector using the SiglipModel
        actual_siglip_vector = siglip_model.compute_features(data_list=[image_path], data_type="image")[0].tolist()
        
        
        ip_asset = IPAsset(
            ip_type=ip_type, # Assuming Trademark for this test image
            id=Common.uuid_utils.generate_uuid(reg_no), # Use the generated point_id as the IP asset ID
            metadata={"ser_no": reg_no, "ip_owner": "Test Owner", "reg_no": reg_no}, # Add necessary metadata
            siglip_vector=actual_siglip_vector
        )

        payload = {
            "ip_assets": [
                {
                    "ip_type": ip_asset.ip_type,
                    "id": ip_asset.id,
                    "metadata": ip_asset.metadata,
                    "siglip_vector": ip_asset.siglip_vector
                }
            ],
            "threshold": None,
            "top_n": None
        }
        
        QDRANT_API_URL = os.environ["QDRANT_API_URL"]
        QDRANT_API_KEY = os.environ["QDRANT_API_KEY"]
        
        # Make the API request to the reverse_check endpoint
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{QDRANT_API_URL}/reverse_check",
                json=payload,
                headers={
                    "Authorization": f"Bearer {QDRANT_API_KEY}",
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status != 200:
                    error_details = await response.text()
                    print(f"Error from Qdrant API: {response.status} - {error_details}")
                    # Depending on desired behavior, you might want to raise an exception or return an empty list
                    # For now, just print and return an empty list as in the original code's error handling
                    # return []
                
                response = await response.json()
                print("The results is in the reverse_check_results in the database!")


    asyncio.run(server_reverse_check_example())