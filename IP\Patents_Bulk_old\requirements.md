The objective is to extract the historical patent data from the USPTO and store it in a database. Then on a weekly basis update the data with the latest patent Grant Full Text Data with Embedded TIFF Images, and on a monthly basis update the CPC data.

We have already done something similar for Trademarks in the IP/Trademarks folder. You study this carefully so as to follow a similar structure for Patent (in IP/Patent folder).

1. Patent Grant Full Text Data with Embedded TIFF Images - XML (PTGRDT):
-  Purpose: To access the patent grant information in a structured, machine-readable format suitable for data processing and analysis. This dataset includes the full text and associated images (drawings, figures).
- Use Open Data portal API similar to Trademark. this is the link: https://data.uspto.gov/bulkdata/datasets/ptgrdt?fileDataFromDate=2025-01-1&fileDataToDate=2025-04-16
- It is released weekly
- Inside the zip file this is the structure: 
Ixxxxx
   DESIGN   -> we need this
      Here we have made many zip file, each have 1 XML with the data and multiple TIFF images
   DTDS     -> we don't need this
   PLANT     -> we don't need this
   REISSUE   -> we don't need this
   UTILxxxxx   -> we need this
      Here we have made many zip file, each have 1 XML with the data and multiple TIFF images
Ixxxxx-SUPP  -> we don't need this

- Format: XML files is is like this:

<us-patent-grant lang="EN" dtd-version="v4.7 2022-02-17" file="USD1070243-20250415.XML" status="PRODUCTION" id="us-patent-grant" country="US" date-produced="20250325" date-publ="20250415">
<us-bibliographic-data-grant>
<publication-reference>
<document-id>
<country>US</country>
<doc-number>D1070243</doc-number>
<kind>S1</kind>
<date>20250415</date>
</document-id>
</publication-reference>
<application-reference appl-type="design">
<document-id>
<country>US</country>
<doc-number>29977244</doc-number>
<date>20241206</date>
</document-id>
</application-reference>
<us-application-series-code>29</us-application-series-code>
<us-term-of-grant>
<length-of-grant>15</length-of-grant>
</us-term-of-grant>
<classification-locarno>
<edition>15</edition>
<main-classification>0201</main-classification>
</classification-locarno>
<classification-national>
<country>US</country>
<main-classification>D 2706</main-classification>
</classification-national>
<invention-title id="d2e53">Underwear</invention-title>
<us-references-cited>
<us-citation>
<patcit num="00001">
<document-id>
<country>US</country>
<doc-number>3430632</doc-number>
<kind>A</kind>
<name>James</name>
<date>19690300</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-cpc-text>A41C 3/00</classification-cpc-text>
<classification-national>
<country>US</country>
<main-classification>450 86</main-classification>
</classification-national>
</us-citation>
<us-citation>
<patcit num="00002">
<document-id>
<country>US</country>
<doc-number>D804778</doc-number>
<kind>S</kind>
<name>Jameson</name>
<date>20171200</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-national>
<country>US</country>
<main-classification>D 2706</main-classification>
</classification-national>
</us-citation>
<us-citation>
<patcit num="00003">
<document-id>
<country>US</country>
<doc-number>D865325</doc-number>
<kind>S</kind>
<name>Carpenter</name>
<date>20191100</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-national>
<country>US</country>
<main-classification>D 2706</main-classification>
</classification-national>
</us-citation>
<us-citation>
<patcit num="00004">
<document-id>
<country>US</country>
<doc-number>D987934</doc-number>
<kind>S</kind>
<name>McKeen et al.</name>
<date>20230600</date>
</document-id>
</patcit>
<category>cited by applicant</category>
</us-citation>
<us-citation>
<patcit num="00005">
<document-id>
<country>US</country>
<doc-number>D998934</doc-number>
<kind>S</kind>
<name>Zheng</name>
<date>20230900</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-national>
<country>US</country>
<main-classification>D 2706</main-classification>
</classification-national>
</us-citation>
<us-citation>
<patcit num="00006">
<document-id>
<country>US</country>
<doc-number>D1001419</doc-number>
<kind>S</kind>
<name>Martinez</name>
<date>20231000</date>
</document-id>
</patcit>
<category>cited by applicant</category>
</us-citation>
<us-citation>
<patcit num="00007">
<document-id>
<country>US</country>
<doc-number>D1019065</doc-number>
<kind>S</kind>
<name>Zhong</name>
<date>20240300</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-national>
<country>US</country>
<main-classification>D 2706</main-classification>
</classification-national>
</us-citation>
<us-citation>
<patcit num="00008">
<document-id>
<country>US</country>
<doc-number>D1030219</doc-number>
<kind>S</kind>
<name>Zhu</name>
<date>20240600</date>
</document-id>
</patcit>
<category>cited by applicant</category>
</us-citation>
<us-citation>
<patcit num="00009">
<document-id>
<country>US</country>
<doc-number>2016/0183602</doc-number>
<kind>A1</kind>
<name>Rider</name>
<date>20160600</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-cpc-text>A61M 1/06</classification-cpc-text>
<classification-national>
<country>US</country>
<main-classification>450 36</main-classification>
</classification-national>
</us-citation>
<us-citation>
<patcit num="00010">
<document-id>
<country>US</country>
<doc-number>2024/0041136</doc-number>
<kind>A1</kind>
<name>Wells</name>
<date>20240200</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-cpc-text>A41C 3/04</classification-cpc-text>
</us-citation>
<us-citation>
<nplcit num="00011">
<othercit>Comfelie bra, posted Jun. 27, 2023 [online], (retrieved Jan. 21, 2025). Retrieved from the internet, https://www.amazon.com/dp/B0CG8J2P97/ref=sspa_dk_detail_6?psc=1&pd_rd_i=B0CG8J2P97&pd_rd_w=3KUWf&content-id=amzn1.sym.c4606765%E2%80%A6 (Year: 2023).</othercit>
</nplcit>
<category>cited by examiner</category>
</us-citation>
<us-citation>
<nplcit num="00012">
<othercit>Spann bra, posted Oct. 12, 2023 [online], (retrieved Jan. 21, 2025). Retrieved from the internet, https://www.amazon.com/Comfort-Wireless-Shaper-Posture-Correction/dp/B0CKXNGQ7X (Year: 2023).</othercit>
</nplcit>
<category>cited by examiner</category>
</us-citation>
</us-references-cited>
<number-of-claims>1</number-of-claims>
<us-exemplary-claim>1</us-exemplary-claim>
<us-field-of-classification-search>
<classification-national>
<country>US</country>
<main-classification>D 2700</main-classification>
</classification-national>
<classification-national>
<country>US</country>
<main-classification>D 2706</main-classification>
</classification-national>
<classification-national>
<country>US</country>
<main-classification>D 2708</main-classification>
</classification-national>
<classification-national>
<country>US</country>
<main-classification>D 2731</main-classification>
</classification-national>
<classification-cpc-text>A41C 3/00</classification-cpc-text>
<classification-cpc-text>A41C 3/0057</classification-cpc-text>
<classification-cpc-text>A41C 3/0007</classification-cpc-text>
<classification-cpc-text>A41C 3/005</classification-cpc-text>
<classification-cpc-text>A41C 3/04</classification-cpc-text>
</us-field-of-classification-search>
<figures>
<number-of-drawing-sheets>7</number-of-drawing-sheets>
<number-of-figures>7</number-of-figures>
</figures>
<us-parties>
<us-applicants>
<us-applicant sequence="001" app-type="applicant" designation="us-only">
<addressbook>
<last-name>Peng</last-name>
<first-name>Xiaoni</first-name>
<address>
<city>Pucheng</city>
<country>CN</country>
</address>
</addressbook>
<residence>
<country>CN</country>
</residence>
</us-applicant>
</us-applicants>
<inventors>
<inventor sequence="001" designation="us-only">
<addressbook>
<last-name>Peng</last-name>
<first-name>Xiaoni</first-name>
<address>
<city>Pucheng</city>
<country>CN</country>
</address>
</addressbook>
</inventor>
</inventors>
<agents>
<agent sequence="01" rep-type="attorney">
<addressbook>
<last-name>Cohn</last-name>
<first-name>Daniel M.</first-name>
<address>
<country>unknown</country>
</address>
</addressbook>
</agent>
<agent sequence="02" rep-type="attorney">
<addressbook>
<last-name>Cohn</last-name>
<first-name>Howard M.</first-name>
<address>
<country>unknown</country>
</address>
</addressbook>
</agent>
</agents>
</us-parties>
<examiners>
<primary-examiner>
<last-name>Thorn, Sr.</last-name>
<first-name>J.</first-name>
<department>2922</department>
</primary-examiner>
</examiners>
</us-bibliographic-data-grant>
<drawings id="DRAWINGS">
<figure id="Fig-EMI-D00000" num="00000">
<img id="EMI-D00000" he="79.93mm" wi="158.75mm" file="USD1070243-20250415-D00000.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00001" num="00001">
<img id="EMI-D00001" he="209.63mm" wi="152.48mm" file="USD1070243-20250415-D00001.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00002" num="00002">
<img id="EMI-D00002" he="200.58mm" wi="162.48mm" file="USD1070243-20250415-D00002.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00003" num="00003">
<img id="EMI-D00003" he="200.41mm" wi="167.47mm" file="USD1070243-20250415-D00003.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00004" num="00004">
<img id="EMI-D00004" he="200.58mm" wi="97.96mm" file="USD1070243-20250415-D00004.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00005" num="00005">
<img id="EMI-D00005" he="203.88mm" wi="152.23mm" file="USD1070243-20250415-D00005.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00006" num="00006">
<img id="EMI-D00006" he="196.09mm" wi="148.25mm" file="USD1070243-20250415-D00006.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00007" num="00007">
<img id="EMI-D00007" he="229.87mm" wi="68.50mm" file="USD1070243-20250415-D00007.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
</drawings>
<description id="description">
<?brief-description-of-drawings description="Brief Description of Drawings" end="lead"?>
<description-of-drawings>
<p id="p-0001" num="0001">
<figref idref="DRAWINGS">
FIG.
<b>1</b>
</figref>
is a perspective view of underwear showing my new design;
</p>
<p id="p-0002" num="0002">
<figref idref="DRAWINGS">
FIG.
<b>2</b>
</figref>
is a front elevational view thereof;
</p>
<p id="p-0003" num="0003">
<figref idref="DRAWINGS">
FIG.
<b>3</b>
</figref>
is a rear elevational view thereof;
</p>
<p id="p-0004" num="0004">
<figref idref="DRAWINGS">
FIG.
<b>4</b>
</figref>
is a left side elevational view thereof; the right side view being the same;
</p>
<p id="p-0005" num="0005">
<figref idref="DRAWINGS">
FIG.
<b>5</b>
</figref>
is another perspective view thereof;
</p>
<p id="p-0006" num="0006">
<figref idref="DRAWINGS">
FIG.
<b>6</b>
</figref>
is a top, front, and left perspective view of the underwear of
<figref idref="DRAWINGS">
FIG.
<b>1</b>
</figref>
, shown in a configuration where the modesty flap is torn open; and,
</p>
<p id="p-0007" num="0007">
<figref idref="DRAWINGS">
FIG.
<b>7</b>
</figref>
is a top view of the Underwear of
<figref idref="DRAWINGS">
FIG.
<b>1</b>
</figref>
, shown open and flattened in a second configuration.
</p>
<p id="p-0008" num="0008">The dash-dash broken lines in the drawings depict the model of the Underwear, and these portions do not form part of the claimed design.</p>
</description-of-drawings>
<?brief-description-of-drawings description="Brief Description of Drawings" end="tail"?>
</description>
<us-claim-statement>CLAIM</us-claim-statement>
<claims id="claims">
<claim id="CLM-00001" num="00001">
<claim-text>The ornamental design for underwear as shown and described.</claim-text>
</claim>
</claims>
</us-patent-grant>




3. Cooperative Patent Classification (CPC) Master Classification Files (CPCMCPT):
- Use Open Data portal API similar to Trademark. this is the link: https://data.uspto.gov/bulkdata/datasets/cpcmcapp?fileDataFromDate=2025-03-27&fileDataToDate=2025-04-16
- The file is updated every month.
- Data strcutures, one zip has multiple XML, each XML has multiple records (each record is 1 patent). This is one record: 

<uspat:CPCMasterClassificationRecord>

<pat:ApplicationIdentification>
<com:IPOfficeCode>US</com:IPOfficeCode>
<com:ApplicationNumber><com:ApplicationNumberText></com:ApplicationNumberText></com:ApplicationNumber>
</pat:ApplicationIdentification>

<pat:PatentGrantIdentification>
<com:IPOfficeCode>US</com:IPOfficeCode>
<pat:PatentNumber>2200000</pat:PatentNumber>
<com:PatentDocumentKindCode>A </com:PatentDocumentKindCode>
<pat:GrantDate>1940-05-07</pat:GrantDate>
</pat:PatentGrantIdentification>

<pat:CPCClassificationBag>
<pat:MainCPC>
<pat:CPCClassification><pat:ClassificationVersionDate>2013-01-01</pat:ClassificationVersionDate><pat:CPCSection>B</pat:CPCSection><pat:Class>65</pat:Class><pat:Subclass>H</pat:Subclass><pat:MainGroup>19</pat:MainGroup><pat:Subgroup>2284</pat:Subgroup><com:SymbolPositionCode>F</com:SymbolPositionCode><pat:CPCClassificationValueCode>I</pat:CPCClassificationValueCode>
</pat:CPCClassification>
</pat:MainCPC>
<pat:FurtherCPC>
<pat:CPCClassification><pat:ClassificationVersionDate>2013-01-01</pat:ClassificationVersionDate><pat:CPCSection>B</pat:CPCSection><pat:Class>65</pat:Class><pat:Subclass>H</pat:Subclass><pat:MainGroup>2301</pat:MainGroup><pat:Subgroup>4148</pat:Subgroup><com:SymbolPositionCode>L</com:SymbolPositionCode><pat:CPCClassificationValueCode>A</pat:CPCClassificationValueCode></pat:CPCClassification>
<pat:CPCClassification><pat:ClassificationVersionDate>2013-01-01</pat:ClassificationVersionDate><pat:CPCSection>B</pat:CPCSection><pat:Class>65</pat:Class><pat:Subclass>H</pat:Subclass><pat:MainGroup>2301</pat:MainGroup><pat:Subgroup>41485</pat:Subgroup><com:SymbolPositionCode>L</com:SymbolPositionCode><pat:CPCClassificationValueCode>A</pat:CPCClassificationValueCode></pat:CPCClassification>
</pat:FurtherCPC>
</pat:CPCClassificationBag>

</uspat:CPCMasterClassificationRecord>


- This data will need to be merged with the Patent Grant Full Text Data using Patent Number

4. Based on CPC data fields, add the required columns to the Patent table in postgres_setup.py. We want these fields from the CPC XML file:
Grant
Date
KindCode
ApplicationNumber
CPCSection
Class 
Subclass
MainGroup
Subgroup
SymbolPositionCode
CPCClassificationValueCode
ClassificationVersionDate